<script lang="ts">
  import { getShapesState, getDimensionsState } from "$lib/stores/state.svelte";
  import {
    Layer,
    Circle,
    Image,
    Group,
    Rect,
    Text,
    Transformer,
  } from "svelte-konva";
  import type { KonvaPointerEvent, KonvaMouseEvent, Stage } from "svelte-konva";
  import * as ShapesUtil from "$lib/canvas.util";
  import ResponsiveCanvas from "./responsive-canvas.svelte";
  import Konva from "konva";
  import type { Konva as KonvaType } from "konva";

  let layer: ReturnType<typeof Layer> | undefined;
  let stage: ReturnType<typeof Stage> | undefined = $state();
  let transformer: ReturnType<typeof Transformer> | undefined;
  let selectionRectangle: ReturnType<typeof Rect> | undefined;

  const SELECTION_RECTANGLE_NAME = "selection-rectangle";

  const shapesState = getShapesState();

  let selectionRectangleConfig = $state({
    fill: "red",
    visible: false,
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    name: SELECTION_RECTANGLE_NAME,
    type: "selectionRect",
  });

  // Used to calculate the position and size of the selection rectangle during selection
  let initialSelectionCoordinates: KonvaType.Vector2d = $state({
    x: 0,
    y: 0,
  });

  let selectionActive = $state(false); // If the transformer is active eg. something is selected

  function selectStart(e: KonvaPointerEvent) {
    if (!transformer || !stage) return;

    const stageNode = stage.node;

    // Check if event target is stage (eg. user clicked on empty part of the stage and not any shape)
    if (e.target.getType() !== "Stage") {
      return;
    }

    // If there is already a selection active, cancel it
    if (selectionActive) {
      transformer.node.nodes([]);
      selectionActive = false;
      return;
    }

    const pointerPos = ShapesUtil.getRealPointerPos(
      stageNode.getPointerPosition()!,
      stageNode
    );

    selectionRectangleConfig.x = pointerPos.x;
    selectionRectangleConfig.y = pointerPos.y;

    initialSelectionCoordinates.x = pointerPos.x;
    initialSelectionCoordinates.y = pointerPos.y;

    selectionRectangleConfig.visible = true;
  }

  function selectDrag() {
    if (!stage) return;

    const node = stage.node;

    if (!selectionRectangleConfig.visible) {
      // Currently no selection is active (eg. user is just moving the cursor around)
      return;
    }

    const pointerPos = ShapesUtil.getRealPointerPos(
      node.getPointerPosition()!,
      node
    );

    // Set new x coordinate and width of selection rectangle
    selectionRectangleConfig.x = Math.min(
      pointerPos.x,
      initialSelectionCoordinates.x
    );
    selectionRectangleConfig.width = Math.abs(
      pointerPos.x - initialSelectionCoordinates.x
    );

    // Set new y coordinate and height of selection rectangle
    selectionRectangleConfig.y = Math.min(
      pointerPos.y,
      initialSelectionCoordinates.y
    );
    selectionRectangleConfig.height = Math.abs(
      pointerPos.y - initialSelectionCoordinates.y
    );
  }

  function selectEnd() {
    if (!layer || !transformer || !selectionRectangle) return;

    if (!selectionRectangleConfig.visible) {
      // Currently no selection is active (eg. user clicked on non empty part of the stage)
      return;
    }

    if (layer.node.children) {
      const selectedEntities = layer.node.children.filter(
        (child) =>
          child.name() !== SELECTION_RECTANGLE_NAME &&
          Konva.Util.haveIntersection(
            selectionRectangle!.node.getClientRect(),
            child.getClientRect()
          )
      );

      if (selectedEntities.length !== 0) {
        // Add all selected shapes etc. to the transformer
        transformer.node.nodes(selectedEntities);

        selectionActive = true;
      }
    }

    selectionRectangleConfig.visible = false;
    selectionRectangleConfig.width = 0;
    selectionRectangleConfig.height = 0;
  }

  // Cancel active selection if mouse cursor leaves stage area
  function selectMouseOut(e: KonvaMouseEvent) {
    // Check if event target is stage (eg. user clicked on empty part of the stage and not any shape)
    if (e.target.getType() !== "Stage") {
      return;
    }

    selectEnd();
  }

  function handleDragEnd(e: any) {
    const id = e.target.attrs.id;
    const newX = e.target.attrs.x;
    const newY = e.target.attrs.y;
    shapesState.update((shapes) =>
      shapes.map((v) => (v.id === id ? { ...v, x: newX, y: newY } : v))
    );
  }

  function loadImage(src: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new window.Image();
      img.crossOrigin = "anonymous";
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = src;
    });
  }
</script>

<div class="h-vh flex items-center justify-center p-4">
  <div class="bg-white rounded-lg">
    <ResponsiveCanvas
      onpointerdown={selectStart}
      onpointermove={selectDrag}
      onpointerup={selectEnd}
      onmouseout={selectMouseOut}
      bind:stage
    >
      <Layer>
        {#each $shapesState.filter((s) => ShapesUtil.isImage(s)) as imageShape}
          {#await loadImage(imageShape.src)}
            <p>loading...</p>
          {:then imageElement}
            <Image {...imageShape} image={imageElement} />
          {/await}
        {/each}
      </Layer>
      <Layer bind:this={layer}>
        {#each $shapesState.filter((s) => !ShapesUtil.isImage(s)) as shape}
          {#if ShapesUtil.isCircle(shape)}
            <Circle {...shape} ondragend={handleDragEnd} />
          {:else if ShapesUtil.isGroupedRectText(shape)}
            {@const groupConfig = {
              id: shape.id,
              x: shape.x,
              y: shape.y,
              draggable: shape.draggable,
              scaleX: shape.scaleX,
              scaleY: shape.scaleY,
              rotation: shape.rotation,
            }}
            {@const rectConfig = {
              x: shape.paddingX,
              y: shape.paddingY,
              width: shape.rectWidth,
              height: shape.rectHeight,
              fill: shape.rectFill,
              cornerRadius: shape.rectCornerRadius,
              fillLinearGradientStartPoint: shape.rectGradientStartPoint,
              fillLinearGradientEndPoint: shape.rectGradientEndPoint,
              fillLinearGradientColorStops: shape.rectGradientColorStops,
            }}
            {@const textConfig = {
              x: shape.paddingX,
              y: shape.paddingY,
              width: shape.rectWidth,
              height: shape.rectHeight,
              text: shape.text,
              fontSize: shape.fontSize,
              fontFamily: shape.fontFamily,
              fill: shape.textFill,
              align: "center",
              verticalAlign: "middle",
            }}
            <Group {...groupConfig} ondragend={handleDragEnd}>
              <Rect {...rectConfig} />
              <Text {...textConfig} />
            </Group>
          {:else if ShapesUtil.isSelectionRect(shape)}
            <Rect {...shape} bind:this={selectionRectangle} />
          {/if}
        {/each}
        <Transformer bind:this={transformer} />
      </Layer>
    </ResponsiveCanvas>
  </div>
</div>

<!-- <div class="h-vh flex items-center justify-center p-4">
    <div class="bg-white rounded-lg border-2 border-dashed border-gray-300" style:width={$dimensionsState.width} style:height={$dimensionsState.height}>
        <Stage config={{ width: $dimensionsState.width, height: $dimensionsState.height }}>
            <Layer>
                {#each $shapesState.filter(s => ShapesUtil.isImage(s)) as imageShape}
                    {#await loadImage(imageShape.src)}
                        <p>loading...</p>
                    {:then imageElement }
                        <Image config={{ ... imageShape, image: imageElement}} />
                    {/await}
                {/each}
            </Layer>
            <Layer>
                {#each $shapesState.filter(s => !ShapesUtil.isImage(s)) as shape}
                    {#if ShapesUtil.isCircle(shape)}
                        <Circle
                            config={{...shape}}
                            on:dragend={handleDragEnd}
                        />
                    {:else if ShapesUtil.isGroupedRectText(shape)}
                        {@const groupConfig = {
                            id: shape.id,
                            x: shape.x,
                            y: shape.y,
                            draggable: shape.draggable,
                            scaleX: shape.scaleX,
                            scaleY: shape.scaleY,
                            rotation: shape.rotation
                        }}
                        {@const rectConfig = {
                            x: shape.paddingX,
                            y: shape.paddingY,
                            width: shape.rectWidth,
                            height: shape.rectHeight,
                            fill: shape.rectFill,
                            cornerRadius: shape.rectCornerRadius,
                            fillLinearGradientStartPoint: shape.rectGradientStartPoint,
                            fillLinearGradientEndPoint: shape.rectGradientEndPoint,
                            fillLinearGradientColorStops: shape.rectGradientColorStops
                        }}
                        {@const textConfig = {
                            x: shape.paddingX,
                            y: shape.paddingY,
                            width: shape.rectWidth,
                            height: shape.rectHeight,
                            text: shape.text,
                            fontSize: shape.fontSize,
                            fontFamily: shape.fontFamily,
                            fill: shape.textFill,
                            align: 'center',
                            verticalAlign: 'middle'
                        }}
                        <Group config={groupConfig} on:dragend={handleDragEnd}>
                            <Rect config={rectConfig} />
                            <Text config={textConfig} />
                        </Group>
                    {/if}
                {/each}
            </Layer>
        </Stage>
    </div>
</div> -->
