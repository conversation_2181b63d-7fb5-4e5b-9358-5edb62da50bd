<script lang="ts">
  import { onMount, type Snippet } from "svelte";
    import { Stage, Layer } from "svelte-konva";
    import type Kon<PERSON> from "konva";
    import type { KonvaPointerEvent } from "svelte-konva";
    import type { KonvaMouseEvent } from "svelte-konva";

    // component props
    type Props = {
        children: Snippet,
        stage?: ReturnType<typeof Stage> | undefined, 
    } & KonvaPointerEvent & KonvaMouseEvent;


    let {
        children,
        stage = $bindable(),
        onpointerdblclick,
		onpointerdown,
		onpointerup,
		onpointermove,
		onmouseout
    }: Props = $props();

    let container: HTMLDivElement;

    const BASE_WIDTH_STAGE = 1000;

    let stageConfig = $state({
        width: BASE_WIDTH_STAGE,
        height: 1000,
        scaleX: 1,
        scaleY: 1,
    });

    function handleStageResize() {
        if (!container) return;

        stageConfig.width = container.offsetWidth - 10;
        stageConfig.height = container.offsetHeight - 10;
        let scale = stageConfig.width / BASE_WIDTH_STAGE;
        stageConfig.scaleX = scale;
        stageConfig.scaleY = scale;
    }

    onMount(() => {
        window.addEventListener("resize", handleStageResize);
        handleStageResize();
    });
</script>


<div bind:this={container} style="max-height: 70vh;">
	<Stage
		{...stageConfig}
		divWrapperProps={{ style: 'border: solid grey 5px;' }}
		{onpointerdblclick}
		{onpointerdown}
		{onpointerup}
		{onpointermove}
		{onmouseout}
	>
		{@render children()}
	</Stage>
</div>