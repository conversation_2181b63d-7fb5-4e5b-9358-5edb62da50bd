<script lang="ts">
    import { <PERSON><PERSON>r as MenubarPrimitive } from "bits-ui";
    import { cn } from "$lib/utils.js";

    let {
        ref = $bindable(null),
        class: className,
        ...restProps
    }: MenubarPrimitive.RootProps = $props();
</script>

<MenubarPrimitive.Root
    bind:ref
    data-slot="menubar"
    class={cn(
        "bg-[#0f172a] shadow-xs flex h-9 items-center gap-1 border-b p-1",
        className,
    )}
    {...restProps}
/>
