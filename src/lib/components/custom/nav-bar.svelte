<script lang="ts">
    import * as <PERSON><PERSON><PERSON> from "$lib/components/ui/menubar/index.js";
    import { getLeftSideState } from "$lib/stores/state.svelte";
    const leftSideState = getLeftSideState();
</script>

<div>
    <Menubar.Root
        class="hover:bg-transparent focus:bg-transparent active:bg-transparent px-5"
    >
        <Menubar.Menu>
            <Menubar.Trigger>Commands</Menubar.Trigger>
            <Menubar.Content>
                {#if !$leftSideState}
                    <Menubar.Item onclick={() => leftSideState.update(v => !v)}>Open Elements</Menubar.Item>
                {/if}
                <Menubar.Item disabled>Print</Menubar.Item>
            </Menubar.Content>
        </Menubar.Menu>
    </Menubar.Root>
</div>
