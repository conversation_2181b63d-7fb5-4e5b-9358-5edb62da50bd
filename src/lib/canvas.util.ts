import type Konva from "konva";

export type ShapeBase = {
  id: string
  x: number
  y: number
  fill?: string
  draggable?: boolean
  scaleX?: number
  scaleY?: number
  rotation?: number
}

export type CircleShape = ShapeBase & {
  type: "circle"
  radius: number
}

export type RectShape = ShapeBase & {
  type: "rect"
  width: number
  height: number
  cornerRadius?: number
}

export type TextShape = ShapeBase & {
    type: "text"
    text: string
    fontSize: number
    fontFamily?: string
}

export type GroupedRectText = ShapeBase & {
  type: "groupedRectText"
  // group-level position (x,y)
  paddingX: number
  paddingY: number
  rectWidth: number
  rectHeight: number
  rectCornerRadius?: number
  rectFill?: string
  rectGradientStartPoint?: { x: number; y: number }
  rectGradientEndPoint?: { x: number; y: number }
  rectGradientColorStops?: Array<number | string>
  text: string
  textFill?: string
  fontSize: number
  fontFamily?: string
}

export type ImageShape = ShapeBase & {
  type: "image"
  src: string
  width: number
  height: number
}

export type Shape = CircleShape | RectShape | TextShape | GroupedRectText | ImageShape;

export function isCircle(shape: Shape): shape is CircleShape {
  return shape.type === "circle";
}

export function isRect(shape: Shape): shape is RectShape {
  return shape.type === "rect";
}

export function isText(shape: Shape): shape is TextShape {
    return shape.type === "text";
}

export function isGroupedRectText(shape: Shape): shape is GroupedRectText {
  return shape.type === "groupedRectText";
}

export function isImage(shape: Shape): shape is ImageShape {
  return shape.type === "image";
}

// Calculate real pointer position on canvas taking scale and stage position into account
// This is required if you scale or reposition your stage x/y coordinates
export function getRealPointerPos(pos: Konva.Vector2d, stage: Konva.Stage) {
  const realPos = {
		x: 0,
		y: 0
	};

	const stageScale = stage.scaleX(); // Only care about x scale as y is always the same

	realPos.x = pos.x / stageScale - stage.x() / stageScale;
	realPos.y = pos.y / stageScale - stage.y() / stageScale;

	return realPos;
}