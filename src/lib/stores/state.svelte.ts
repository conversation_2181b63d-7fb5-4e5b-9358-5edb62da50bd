import { getContext, setContext } from "svelte";
import type { Writable } from "svelte/store";
import { writable } from "svelte/store";
import type { Shape } from "$lib/canvas.util";
import type { Dimensions } from "$lib/other.types";


// KEYS
const SHAPES_CTX = "SHAPES_CTX";
const DIMENSIONS_CTX = "DIMENSIONS_CTX";
const LEFT_SIDE_CTX = "LEFT_SIDE_CTX";
// setters
export function setShapesState(shapes: Shape[]) {
  const shapesState = writable(shapes);
  setContext(SHAPES_CTX, shapesState);

  return shapesState;
}

export function setDimensionsState(dimensions: Dimensions) {
  const dimensionState = writable(dimensions);
  setContext(DIMENSIONS_CTX, dimensionState);

  return dimensionState;
}

export function setLeftSideState(open: boolean){
  const leftSideState = writable(open);
  setContext(LEFT_SIDE_CTX, leftSideState);

  return leftSideState;
}


// getters
export function getShapesState() {
  return getContext<Writable<Shape[]>>(SHAPES_CTX);
}

export function getDimensionsState() {
  return getContext<Writable<Dimensions>>(DIMENSIONS_CTX);
}

export function getLeftSideState() {
  return getContext<Writable<boolean>>(LEFT_SIDE_CTX);
}
