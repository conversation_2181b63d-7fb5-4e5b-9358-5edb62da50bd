<script lang="ts">
    import "../app.css";
    import favicon from "$lib/assets/favicon.svg";
    import NavBar from "$lib/components/custom/nav-bar.svelte";
    import LeftSidebar from "$lib/components/custom/left-sidebar.svelte";
    import '@fontsource/geist-sans';
    // Supports weights 100-900
    import '@fontsource-variable/archivo';
    // Supports weights 
    import '@fontsource/barlow';
    import { setShapesState, setDimensionsState, setLeftSideState } from "$lib/stores/state.svelte";
    import { Toaster } from "$lib/components/ui/sonner/index.js";
    import { screens } from "$lib/constants";
    import { calculateDimensions } from "$lib/utils";

    let { children } = $props();
    setShapesState([]);
    setDimensionsState(calculateDimensions(screens[0].aspectRatio, window.innerWidth, window.innerHeight, screens[0].maxWidth, screens[0].maxHeight));
    setLeftSideState(true);
</script>

<svelte:head>
    <link rel="icon" href={favicon} />
    <title>Editor - MGT</title>
</svelte:head>

<Toaster />
<NavBar />
<LeftSidebar />
<main>
    {@render children?.()}
</main>
