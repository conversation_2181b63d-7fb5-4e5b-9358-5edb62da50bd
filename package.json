{"name": "editor-<PERSON><PERSON><PERSON><PERSON><PERSON>-svel<PERSON>", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.515.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/vite": "^4.0.0", "@types/node": "^22", "bits-ui": "^2.8.6", "clsx": "^2.1.1", "drizzle-kit": "^0.30.2", "mode-watcher": "^1.0.8", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "svelte-sonner": "^1.0.5", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.6", "typescript": "^5.0.0", "vite": "^7.0.4", "vite-plugin-devtools-json": "^0.2.0"}, "dependencies": {"@fontsource-variable/archivo": "^5.2.6", "@fontsource-variable/noto-sans-jp": "^5.2.6", "@fontsource/barlow": "^5.2.6", "@fontsource/geist-sans": "^5.2.5", "@libsql/client": "^0.14.0", "drizzle-orm": "^0.40.0", "konva": "^9.3.22", "sharp": "^0.34.3", "svelte-konva": "^1.0.0-next.0"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}