<script lang="ts">
  import { Button } from "$lib/components/ui/button/index.js";
  import {
    getShapesState,
    getDimensionsState,
    getLeftSideState,
  } from "$lib/stores/state.svelte";
  import { calculateDimensions, cn } from "$lib/utils";
  import { X, ImageUp, Circle, TextCursor, BrushCleaning, ImageDown, Save } from "@lucide/svelte/icons";
  import { toast } from "svelte-sonner";
  import { Label } from "$lib/components/ui/label/index.js";
  import * as AlertDialog from "$lib/components/ui/alert-dialog/index.js";
  import { Input } from "$lib/components/ui/input/index.js";
  import { screens } from "$lib/constants";
  import type { Screens } from "$lib/other.types";
  import { innerWidth, innerHeight } from "svelte/reactivity/window";

  const shapeState = getShapesState();
  const dimensionsState = getDimensionsState();
  let leftSideState = getLeftSideState();

  let files: FileList | undefined = $state();
  let isProcessing: boolean = $state(false);
  let enterTextDialogState: boolean = $state(false); 

  let fileInput: HTMLInputElement;

  // input state
  let enterTextState: string = $state("");

  // actions
  function addCircle() {
    shapeState.update((v) => [
      ...v,
      {
        id: `circle_${Date.now()}`,
        type: "circle",
        x: 400,
        y: 150,
        radius: 50,
        fill: "#22c55e",
        draggable: true,
      },
    ]);
  }

  async function addImage() {
    if (!files || !files[0]) {
      toast.error("No images selected");
    }
    const file = files[0];
    isProcessing = true;

    try {
      const formData = new FormData();
      formData.append("image", file);

      const currentDimensions = $dimensionsState;
      formData.append("width", currentDimensions.width.toString());
      formData.append("height", currentDimensions.height.toString());

      const response = await fetch("/api/image", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        toast.error(`HTTP error! status: ${response.status}`);
        return;
      }

      const result = await response.json();

      if (result.success) {
        shapeState.update((v) => [
          ...v,
          {
            id: `image_${Date.now()}`,
            type: "image",
            x: 0,
            y: 0,
            src: result.image,
            width: result.width,
            height: result.height,
          },
        ]);
      }

      if (fileInput) fileInput.value = "";
      files = undefined;
    } catch ({ name, message }: any) {
      toast.error(message);
    } finally {
      isProcessing = false;
    }
  }

  function addGroupedRectText(text: string) {
    if (!text) {
      toast.error("No text entered");
      handleEnterTextDialog();
      return;
    }

    shapeState.update((v) => [
      ...v,
      {
        id: `groupedRectText_${Date.now()}`,
        type: "groupedRectText",
        x: 0,
        y: 260,
        paddingX: 10,
        paddingY: 10,
        rectWidth: 100,
        rectHeight: 50,
        rectCornerRadius: 12,
        rectFill: "",
        rectGradientStartPoint: { x: 0, y: 0 },
        rectGradientEndPoint: { x: 100, y: 90 },
        rectGradientColorStops: [0, "#f87171", 1, "#f87171"],
        text: text,
        textFill: "#ffffff",
        fontSize: 40,
        fontFamily: "Barlow, sans-serif",
        draggable: true,
      },
    ]);
    handleEnterTextDialog();
  }

  function handleEnterTextDialog() {
    enterTextDialogState = !enterTextDialogState;
  }

  // others
  function triggerFileInput() {
    fileInput?.click();
  }

  function handleScreenDimensions(screen: Screens) {
    const newDimensions = calculateDimensions(
      screen.aspectRatio,
      innerWidth.current,
      innerHeight.current,
      screen.maxWidth,
      screen.maxHeight
    );
    dimensionsState.update((v) => (v = newDimensions));
  }
</script>

{#if $leftSideState}
  <aside
    class={cn(
      "fixed left-4 top-15 bg-[#0f172a] text-white",
      "rounded-xl border border-border shadow-2xl",
      "transition-all duration-200",
      "w-80",
      "z-[40]"
    )}
  >
    <div class="flex items-center justify-between px-4 py-3 border-b gap-3">
      <h2>Controls</h2>
      <Button variant="ghost" onclick={() => leftSideState.update((v) => !v)}>
        <X class="size-5" />
      </Button>
    </div>
    <!-- aspect ratio -->
    <div class="py-4 px-4">
      <!-- <div class="flex flex-col gap-2 my-4">
        <Label>Aspect Ratio</Label>
        <div class="grid grid-cols-2 gap-2">
          {#each screens as screen}
            <Button
              variant="secondary"
              onclick={() => handleScreenDimensions(screen)}
              >{screen.name}</Button
            >
          {/each}
        </div>
      </div> -->
      <!-- shapes -->
      <div class="flex flex-col gap-2 my-4">
        <Label>Shapes</Label>
        <Button variant="secondary" onclick={addCircle}>
          <Circle />
          Add Circle
        </Button>
        <Button
          variant="secondary"
          onclick={triggerFileInput}
          disabled={isProcessing}
        >
          <input
            type="file"
            accept="image/*"
            bind:files
            bind:this={fileInput}
            onchange={addImage}
            class="hidden"
          />
          <ImageUp />
          {isProcessing ? "Processing" : "Upload Poster"}
        </Button>
        <Button variant="secondary" onclick={handleEnterTextDialog}>
          <TextCursor />
          Add Review Score
        </Button>
      </div>
       <!-- actions -->
    <div class="flex flex-col gap-2 my-4">
        <Label>Actions</Label>
        <Button variant="secondary">
          <Save />
          Save Stage
        </Button>
        <Button variant="secondary">
          <ImageDown />
          Export to PNG
        </Button>
         <Button variant="destructive" onclick={() => shapeState.set([])} disabled={$shapeState.length === 0}>
          <BrushCleaning />
          Clear All
        </Button>
    </div>
    </div>
   
  </aside>
  <AlertDialog.Root bind:open={enterTextDialogState} onOpenChange={(v) => enterTextDialogState = v} onOpenChangeComplete={(v) => enterTextDialogState = v}>
  <AlertDialog.Content>
    <AlertDialog.Header>
      <AlertDialog.Title>Enter Review Score</AlertDialog.Title>
      <AlertDialog.Description>
        <Input type="text" placeholder="4.5, 8" bind:value={enterTextState} />
      </AlertDialog.Description>
    </AlertDialog.Header>
    <AlertDialog.Footer>
      <AlertDialog.Cancel class="text-gray-700">Cancel</AlertDialog.Cancel>
      <AlertDialog.Action onclick={() => addGroupedRectText(enterTextState)}>Continue</AlertDialog.Action>
    </AlertDialog.Footer>
  </AlertDialog.Content>
</AlertDialog.Root>
{/if}
