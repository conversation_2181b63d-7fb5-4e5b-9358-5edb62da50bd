import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import type { Dimensions } from "$lib/other.types";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type WithoutChild<T> = T extends { child?: any } ? Omit<T, "child"> : T;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type WithoutChildren<T> = T extends { children?: any } ? Omit<T, "children"> : T;
export type WithoutChildrenOrChild<T> = WithoutChildren<WithoutChild<T>>;
export type WithElementRef<T, U extends HTMLElement = HTMLElement> = T & { ref?: U | null };



// custom utils
export function calculateDimensions(aspectRatio: string,
	browserWidth: number | undefined,
	browserHeight: number | undefined,
	maxWidth?: number,
	maxHeight?: number,
	padding: number = 20): Dimensions {
	
	const [ratioW, ratioH] = aspectRatio.split(":").map(Number);
	const ratio = ratioW / ratioH;

	const availableWindowWidth = browserWidth! - padding;
	const availableWindowHeight = browserHeight! - padding;

	let width, height;

	if(availableWindowWidth / availableWindowHeight > ratio){
		height = availableWindowHeight;
		width = height * ratio;
	} else {
		width = availableWindowWidth;
		height = width / ratio;
	}

	if (maxWidth && width > maxWidth) {
		width = maxWidth;
		height = width / ratio;
	}

	if (maxHeight && height > maxHeight) {
		height = maxHeight;
		width = height * ratio;
	}

	return {
		width: Math.floor(width),
		height: Math.floor(height)
	}
}