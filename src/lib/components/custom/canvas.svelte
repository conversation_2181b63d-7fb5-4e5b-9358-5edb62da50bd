<script lang="ts">
    import { getShapesState, getDimensionsState} from "$lib/stores/state.svelte";
    import { Stage, Layer, Circle, Image, Group, Rect, Text } from "svelte-konva";
    import * as ShapesUtil from "$lib/canvas.util";

    const shapesState = getShapesState();
    const dimensionsState = getDimensionsState();

    function handleDragEnd(e: any){
      const id = e.detail.target.attrs.id;
      const newX = e.detail.target.attrs.x;
      const newY = e.detail.target.attrs.y;
      shapesState.update(shapes => shapes.map(v => v.id === id ? { ... v, x: newX, y: newY} : v));
    }

    function loadImage(src: string): Promise<HTMLImageElement> {
      return new Promise((resolve, reject) => {
        const img = new window.Image();
        img.crossOrigin = 'anonymous';
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = src;
      });
    }
  </script>

<div class="h-vh flex items-center justify-center p-4">
    <div class="bg-white rounded-lg border-2 border-dashed border-gray-300" style:width={$dimensionsState.width} style:height={$dimensionsState.height}>
        <Stage config={{ width: $dimensionsState.width, height: $dimensionsState.height }}>
            <Layer>
                {#each $shapesState.filter(s => ShapesUtil.isImage(s)) as imageShape}
                    {#await loadImage(imageShape.src)}
                        <p>loading...</p>
                    {:then imageElement }
                        <Image config={{ ... imageShape, image: imageElement}} />
                    {/await}
                {/each}
            </Layer>
            <Layer>
                {#each $shapesState.filter(s => !ShapesUtil.isImage(s)) as shape}
                    {#if ShapesUtil.isCircle(shape)}
                        <Circle
                            config={{...shape}}
                            on:dragend={handleDragEnd}
                        />
                    {:else if ShapesUtil.isGroupedRectText(shape)}
                        {@const groupConfig = {
                            id: shape.id,
                            x: shape.x,
                            y: shape.y,
                            draggable: shape.draggable,
                            scaleX: shape.scaleX,
                            scaleY: shape.scaleY,
                            rotation: shape.rotation
                        }}
                        {@const rectConfig = {
                            x: shape.paddingX,
                            y: shape.paddingY,
                            width: shape.rectWidth,
                            height: shape.rectHeight,
                            fill: shape.rectFill,
                            cornerRadius: shape.rectCornerRadius,
                            fillLinearGradientStartPoint: shape.rectGradientStartPoint,
                            fillLinearGradientEndPoint: shape.rectGradientEndPoint,
                            fillLinearGradientColorStops: shape.rectGradientColorStops
                        }}
                        {@const textConfig = {
                            x: shape.paddingX,
                            y: shape.paddingY,
                            width: shape.rectWidth,
                            height: shape.rectHeight,
                            text: shape.text,
                            fontSize: shape.fontSize,
                            fontFamily: shape.fontFamily,
                            fill: shape.textFill,
                            align: 'center',
                            verticalAlign: 'middle'
                        }}
                        <Group config={groupConfig} on:dragend={handleDragEnd}>
                            <Rect config={rectConfig} />
                            <Text config={textConfig} />
                        </Group>
                    {/if}
                {/each}
            </Layer>
        </Stage>
    </div>
</div>
