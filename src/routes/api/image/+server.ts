import { error, json } from "@sveltejs/kit";
import type { RequestHandler } from "./$types";
import sharp from "sharp";

export const POST: RequestHandler = async ({ request }) => {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;

    if (!file) {
      throw error(400, 'No image in request');
    }

    const targetWidth = parseInt(formData.get('width') as string) || 600;
    const targetHeight = parseInt(formData.get('height') as string) || 600;

    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    const pi = await sharp(buffer)
      .resize(targetWidth, targetHeight, {
        kernel: sharp.kernel.cubic,
        withoutEnlargement: true,
      })
      .extract({ left: 0, top: 0, width: targetWidth, height: targetHeight })
      .png({ quality: 70 })
      .toBuffer();

    const meta = await sharp(pi).metadata();

    const base64Image = `data:image/png;base64,${pi.toString('base64')}`;

    return json({
      success: true,
      image: base64Image,
      width: meta.width || 0,
      height: meta.height || 0
    });

  } catch ({ name, message }: any) {
    throw error(500, message);
  }
}
