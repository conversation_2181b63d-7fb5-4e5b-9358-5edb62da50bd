<script lang="ts">
    import { Button } from "$lib/components/ui/button/index.js";
    import { onMount } from "svelte";
    import type CanvasComponent from "$lib/components/custom/canvas.svelte";
    import { getLeftSideState } from "$lib/stores/state.svelte";

    let canvas: typeof CanvasComponent;

    const leftSideState = getLeftSideState();

    onMount(async () => {
        canvas = (await import("$lib/components/custom/canvas.svelte")).default;
    });
</script>

<div class="relative" style:padding-left={$leftSideState ? `${12 + 80 + 16}px` : "0px"} style:padding-right="12px" style:padding-top="12px" style:padding-bottom="12px">
    <svelte:component this={canvas} />
</div>
